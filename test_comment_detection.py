#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复评论检测功能
"""

import time
import logging
from playwright.sync_api import sync_playwright
from config import CONFIG

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_comment_detection():
    """测试评论检测功能"""
    
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent=CONFIG['browser']['user_agent']
        )
        page = context.new_page()
        
        try:
            # 访问小红书首页
            logger.info("访问小红书首页...")
            page.goto(CONFIG['base_url'], wait_until='networkidle')
            time.sleep(3)
            
            # 模拟检测逻辑
            logger.info("开始测试评论检测逻辑...")
            
            # 测试检测函数
            result = page.evaluate("""
                () => {
                    // 模拟检测逻辑
                    const commentSelectors = [
                        '.comment-list .comment-item',
                        '[class*="comment-item"]',
                        '.comments .comment',
                        '[data-testid*="comment"]',
                        '.comment',
                        '[class*="Comment"]'
                    ];

                    let allComments = [];
                    for (let selector of commentSelectors) {
                        const elements = document.querySelectorAll(selector);
                        allComments = allComments.concat(Array.from(elements));
                    }

                    console.log('找到评论元素数量:', allComments.length);

                    // 如果没有找到评论，说明可以评论
                    if (allComments.length === 0) {
                        return { hasOwnComment: false, reason: '没有找到评论', commentCount: 0 };
                    }

                    // 检查每个评论是否有删除按钮
                    let foundDeleteButton = false;
                    for (let i = 0; i < allComments.length; i++) {
                        const comment = allComments[i];
                        
                        // 查找三个点按钮
                        const moreButtons = comment.querySelectorAll(
                            'button, [class*="more"], [class*="More"], [class*="menu"], [class*="Menu"], ' +
                            '[class*="action"], [class*="Action"], [class*="option"], [class*="Option"], ' +
                            '.three-dots, [title*="更多"], [aria-label*="更多"]'
                        );
                        
                        if (moreButtons.length > 0) {
                            console.log('找到更多按钮数量:', moreButtons.length);
                            // 这里只是检测逻辑，不实际点击
                            foundDeleteButton = true;
                            break;
                        }
                    }

                    return { 
                        hasOwnComment: false, 
                        reason: `找到${allComments.length}个评论，${foundDeleteButton ? '有' : '无'}更多按钮`,
                        commentCount: allComments.length,
                        hasMoreButtons: foundDeleteButton
                    };
                }
            """)
            
            logger.info(f"检测结果: {result}")
            
            # 等待用户观察
            logger.info("请观察页面，按Enter继续...")
            input()
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
        finally:
            browser.close()

if __name__ == "__main__":
    test_comment_detection()
